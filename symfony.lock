{"doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "620b57f496f2e599a6015a9fa222c2ee0a32adcb"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "phpunit/phpunit": {"version": "12.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "11.1", "ref": "c6658a60fc9d594805370eacdf542c3d6b5c0869"}, "files": [".env.test", "phpunit.dist.xml", "tests/bootstrap.php", "bin/phpunit"]}, "symfony/asset-mapper": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "5ad1308aa756d58f999ffbe1540d1189f5d7d14a"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/asset_mapper.yaml", "importmap.php"]}, "symfony/console": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/form": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "5a1497d539f691b96afd45ae397ce5fe30beb4b9"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php", ".editorconfig"]}, "symfony/mailer": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.63", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["config/packages/notifier.yaml"]}, "symfony/property-info": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "dae70df71978ae9226ae915ffd5fad817f5ca1f7"}, "files": ["config/packages/property_info.yaml"]}, "symfony/routing": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.26", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "3acc494b566816514a6873a89023a35440b6386d"}, "files": ["assets/bootstrap.js", "assets/controllers.json", "assets/controllers/csrf_protection_controller.js", "assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "620a1b84865ceb2ba304c8f8bf2a185fbf32a843"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/ux-turbo": {"version": "2.26", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "c85ff94da66841d7ff087c19cbcd97a2df744ef9"}}, "symfony/validator": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "5b2b543e13942495c0003f67780cb4448af9e606"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webapp-pack": {"version": "1.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "7d5c5e282f7e2c36a2c3bbb1504f78456c352407"}, "files": ["config/packages/messenger.yaml"]}, "symfonycasts/tailwind-bundle": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "0.8", "ref": "4ea7c9488fdce8943520daf3fdc31e93e5b59c64"}, "files": ["config/packages/symfonycasts_tailwind.yaml"]}, "twig/extra-bundle": {"version": "v3.21.0"}}