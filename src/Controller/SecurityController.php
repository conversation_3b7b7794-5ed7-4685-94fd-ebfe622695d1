<?php

namespace App\Controller;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SecurityController extends AbstractController
{
    #[Route('/login', name: 'app_login')]
    public function login(AuthenticationUtils $authenticationUtils): Response
    {
        // if user is already logged in, redirect to dashboard
        if ($this->getUser()) {
            return $this->redirectToRoute('app_dashboard');
        }

        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();
        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->render('security/login.html.twig', [
            'last_username' => $lastUsername,
            'error' => $error,
        ]);
    }

    #[Route('/register', name: 'app_register')]
    public function register(
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager,
        ValidatorInterface $validator
    ): Response {
        // if user is already logged in, redirect to dashboard
        if ($this->getUser()) {
            return $this->redirectToRoute('app_dashboard');
        }

        $user = new User();
        $errors = [];

        if ($request->isMethod('POST')) {
            $email = $request->request->get('email');
            $password = $request->request->get('password');
            $passwordConfirm = $request->request->get('password_confirm');
            $firstName = $request->request->get('first_name');
            $lastName = $request->request->get('last_name');
            $company = $request->request->get('company');
            $phone = $request->request->get('phone');

            // Basic validation
            if (empty($email)) {
                $errors[] = 'Email je povinný.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Zadejte platný email.';
            }

            if (empty($password)) {
                $errors[] = 'Heslo je povinné.';
            } elseif (strlen($password) < 6) {
                $errors[] = 'Heslo musí mít alespoň 6 znaků.';
            }

            if ($password !== $passwordConfirm) {
                $errors[] = 'Hesla se neshodují.';
            }

            if (empty($firstName)) {
                $errors[] = 'Jméno je povinné.';
            }

            if (empty($lastName)) {
                $errors[] = 'Příjmení je povinné.';
            }

            // Check if user already exists
            $existingUser = $entityManager->getRepository(User::class)->findByEmail($email);
            if ($existingUser) {
                $errors[] = 'Uživatel s tímto emailem již existuje.';
            }

            if (empty($errors)) {
                $user->setEmail($email);
                $user->setFirstName($firstName);
                $user->setLastName($lastName);
                $user->setCompany($company);
                $user->setPhone($phone);

                // Hash the password
                $hashedPassword = $passwordHasher->hashPassword($user, $password);
                $user->setPassword($hashedPassword);

                // Validate entity
                $violations = $validator->validate($user);
                if (count($violations) > 0) {
                    foreach ($violations as $violation) {
                        $errors[] = $violation->getMessage();
                    }
                } else {
                    $entityManager->persist($user);
                    $entityManager->flush();

                    $this->addFlash('success', 'Registrace byla úspěšná! Nyní se můžete přihlásit.');
                    return $this->redirectToRoute('app_login');
                }
            }
        }

        return $this->render('security/register.html.twig', [
            'user' => $user,
            'errors' => $errors,
        ]);
    }

    #[Route('/logout', name: 'app_logout')]
    public function logout(): void
    {
        throw new \LogicException('This method can be blank - it will be intercepted by the logout key on your firewall.');
    }
}
